import pandas as pd
from src.db.ibis_support import IbisSupport
from src.utils.crypto_utils import decrypt_password

# These would typically come from environment variables or a secure vault
ENCRYPTED_PASSWORD = "gAAAAABl..."  # Your encrypted password string
FERNET_KEY = "your-fernet-key-here" # Your Fernet key

# Decrypt the password
password = decrypt_password(ENCRYPTED_PASSWORD, FERNET_KEY)

# Initialize IbisSupport
ibis_db = IbisSupport(
    host="localhost",
    port=1521,
    user="myuser",
    password=password,
    database="orclpdb"
)

# Sample SELECT query
query = "SELECT * FROM users WHERE role = :role"
params = {"role": "admin"}
df = ibis_db.query_data(query, params=params)
print(df)

# Sample INSERT (write_table)
data = {
    "user_id": [1, 2],
    "action": ["login", "logout"],
    "timestamp": ["2025-07-25 10:00:00", "2025-07-25 10:05:00"]
}
df_insert = pd.DataFrame(data)
ibis_db.write_table(df_insert, "audit_log", if_exists="replace")