from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

class SQLServerConnector:
    def __init__(self, connection_string):
        self.connection_string = connection_string
        self.engine = create_engine(self.connection_string)
        self.Session = sessionmaker(bind=self.engine)

    def connect(self):
        return self.Session()

    def execute_query(self, query, params=None):
        session = self.connect()
        try:
            result = session.execute(query, params)
            session.commit()
            return result.fetchall()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()