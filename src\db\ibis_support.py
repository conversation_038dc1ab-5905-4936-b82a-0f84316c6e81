import ibis
# Make sure you have ibis-framework[oracle] and pandas installed.
#Adjust the connection parameters as needed for your Oracle setup.
#The write_table method uses create_table for writing; adjust logic if you want to append or handle other if_exists behaviors.
F#or large data, consider chunking or using Oracle-specific bulk loading.

class IbisSupport:
    def __init__(self, host, port, user, password, database, sid=None, service_name=None):
        # Connect to Oracle using ibis
        self.connection = ibis.oracle.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            sid=sid,
            service_name=service_name
        )

    def query_data(self, query, params=None):
        """
        Execute a parameterized query using IBIS to prevent SQL injection.
        :param query: SQL query string with placeholders (e.g., :param1)
        :param params: Dictionary of parameters to bind to the query
        """
        if params:
            # Use IBIS parameterized execution if supported
            return self.connection.raw_sql(query, params=params).to_pandas()
        else:
            return self.connection.raw_sql(query).to_pandas()
    def get_table(self, table_name):
        """
        Safely get a table reference using IBIS.
        Only allow alphanumeric and underscore table names to prevent injection.
        """
        if not table_name.isidentifier():
            raise ValueError("Invalid table name.")
        return self.connection.table(table_name)
    
    def write_table(self, df, table_name, if_exists="fail"):
        # Write a pandas DataFrame to Oracle using ibis
        self.connection.create_table(table_name, df, overwrite=(if_exists == "replace"))

    def close_connection(self):
        # IBIS connections typically do not require explicit close, but add if needed
        if hasattr(self.connection, "close"):
            self.connection.close()
