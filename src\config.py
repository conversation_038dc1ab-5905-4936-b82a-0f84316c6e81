import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your_default_secret_key'
    
    # Database configuration
    ORACLE_DATABASE_URI = os.environ.get('ORACLE_DATABASE_URI') or 'oracle://user:password@host:port/dbname'
    SQLSERVER_DATABASE_URI = os.environ.get('SQLSERVER_DATABASE_URI') or 'mssql+pyodbc://user:password@host:port/dbname?driver=ODBC+Driver+17+for+SQL+Server'
    
    # OAuth configuration
    OAUTH_CLIENT_ID = os.environ.get('OAUTH_CLIENT_ID') or 'your_client_id'
    OAUTH_CLIENT_SECRET = os.environ.get('OAUTH_CLIENT_SECRET') or 'your_client_secret'
    OAUTH_REDIRECT_URI = os.environ.get('OAUTH_REDIRECT_URI') or 'http://localhost:5000/callback'
    
    # IBM Decision Center configuration
    IBM_DECISION_CENTER_URL = os.environ.get('IBM_DECISION_CENTER_URL') or 'https://your_ibm_decision_center_url'
    IBM_DECISION_CENTER_API_KEY = os.environ.get('IBM_DECISION_CENTER_API_KEY') or 'your_api_key'