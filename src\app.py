from flask import Flask
from src.routes.api_routes import setup_routes
from src.auth.oauth import OAuthManager
from src.config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    oauth_manager = OAuthManager(app)
    oauth_manager.init_oauth()

    setup_routes(app)

    return app

if __name__ == "__main__":
    app = create_app()
    app.run(host='0.0.0.0', port=5000)