# Flask API Framework

This project is a generic API framework built using Flask. It includes OAuth for authentication, connectivity to Oracle and SQL Server databases, support for the IBIS library, and integration with IBM Decision Center.

## Features

- **Flask Framework**: A lightweight WSGI web application framework.
- **OAuth Authentication**: Secure authentication mechanism for API access.
- **Database Connectivity**: Connects to Oracle and SQL Server databases.
- **IBIS Library Support**: Enables advanced data querying capabilities.
- **IBM Decision Center Integration**: Facilitates communication with IBM Decision Center.

## Project Structure

```
flask-api-framework
├── src
│   ├── app.py                # Entry point of the application
│   ├── auth
│   │   └── oauth.py          # OAuth authentication logic
│   ├── db
│   │   ├── oracle_connector.py # Oracle database connection
│   │   ├── sqlserver_connector.py # SQL Server database connection
│   │   └── ibis_support.py    # IBIS library integration
│   ├── ibm_decision_center
│   │   └── integration.py      # IBM Decision Center integration
│   ├── routes
│   │   └── api_routes.py       # API route definitions
│   └── config.py              # Configuration settings
├── requirements.txt            # Project dependencies
├── README.md                   # Project documentation
└── .env.example                # Example environment variables
```

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd flask-api-framework
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows use `venv\Scripts\activate`
   ```

3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

4. Set up environment variables by copying `.env.example` to `.env` and filling in the necessary values.

## Usage

To run the application, execute the following command:

```
python src/app.py
```

The API will be available at `http://localhost:5000`.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any improvements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.